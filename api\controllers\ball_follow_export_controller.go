package controllers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"github.com/xuri/excelize/v2"

	. "lottery/models"
)

// BallFollowExportController 版路分析匯出控制器
type BallFollowExportController struct {
	repository *BallFollowRepository
}

// NewBallFollowExportController 建立新的匯出控制器
func NewBallFollowExportController(repo *BallFollowRepository) *BallFollowExportController {
	return &BallFollowExportController{
		repository: repo,
	}
}

// ExportResults 匯出特定期數的Excel報表
func (ctrl *BallFollowExportController) ExportResults(c *gin.Context) {
	lottoType := c.Query("lotto_type")
	periodStr := c.Query("period")
	format := c.DefaultQuery("format", "excel")

	if lottoType == "" || periodStr == "" {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{
			"error":   "參數錯誤",
			"message": "需要提供 lotto_type 和 period 參數",
		})
		return
	}

	period, err := strconv.Atoi(periodStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "參數錯誤",
			"message": "無效的期數",
		})
		return
	}

	// 檢查計算是否完成
	task, err := ctrl.repository.GetCalculationTaskByPeriod(LottoTypeStr(lottoType), period)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "任務不存在",
			"message": "找不到指定期數的計算任務",
		})
		return
	}

	if !task.IsCompleted() {
		c.JSON(http.StatusAccepted, gin.H{
			"error":   "計算尚未完成",
			"message": "計算尚未完成，請稍後再試",
			"progress": map[string]interface{}{
				"completed":  task.CompletedCombinations,
				"total":      task.TotalCombinations,
				"percentage": fmt.Sprintf("%.2f", task.GetProgressPercentage()),
			},
		})
		return
	}

	// 取得計算結果
	results, err := ctrl.repository.GetAnalysisResultsByPeriod(LottoTypeStr(lottoType), period)
	if err != nil {
		log.Errorf("Failed to get analysis results: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "查詢失敗",
			"message": "無法取得計算結果",
		})
		return
	}

	if len(results) == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "結果不存在",
			"message": "找不到指定期數的計算結果",
		})
		return
	}

	// 根據格式匯出
	switch format {
	case "excel":
		ctrl.exportToExcel(c, lottoType, period, results)
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "格式不支援",
			"message": "目前只支援 excel 格式",
		})
	}
}

// exportToExcel 匯出到Excel檔案
func (ctrl *BallFollowExportController) exportToExcel(c *gin.Context, lottoType string, period int, results []BallFollowAnalysisResult) {
	// 建立新的Excel檔案
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			log.Errorf("Failed to close Excel file: %v", err)
		}
	}()

	// 刪除預設工作表
	f.DeleteSheet("Sheet1")

	// 建立工作表
	ctrl.createSummarySheet(f, lottoType, period, results)
	ctrl.createPredictNumbersSheet(f, results)
	ctrl.createNonAppearedNumbersSheet(f, results)
	ctrl.createTailStatisticsSheet(f, results)
	ctrl.createParameterCombinationsSheet(f, results)

	// 設定檔案名稱
	filename := fmt.Sprintf("ball_follow_analysis_%s_%d_%s.xlsx",
		lottoType, period, time.Now().Format("20060102_150405"))

	// 設定回應標頭
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	c.Header("Content-Transfer-Encoding", "binary")

	// 寫入回應
	if err := f.Write(c.Writer); err != nil {
		log.Errorf("Failed to write Excel file: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "匯出失敗",
			"message": "無法生成Excel檔案",
		})
		return
	}
}

// createSummarySheet 建立摘要工作表
func (ctrl *BallFollowExportController) createSummarySheet(f *excelize.File, lottoType string, period int, results []BallFollowAnalysisResult) {
	sheetName := "摘要"
	index, _ := f.NewSheet(sheetName)
	f.SetActiveSheet(index)

	// 設定標題
	f.SetCellValue(sheetName, "A1", "版路分析結果摘要")
	f.SetCellValue(sheetName, "A3", "彩種:")
	f.SetCellValue(sheetName, "B3", lottoType)
	f.SetCellValue(sheetName, "A4", "期數:")
	f.SetCellValue(sheetName, "B4", period)
	f.SetCellValue(sheetName, "A5", "分析日期:")
	f.SetCellValue(sheetName, "B5", time.Now().Format("2006-01-02 15:04:05"))
	f.SetCellValue(sheetName, "A6", "總參數組合數:")
	f.SetCellValue(sheetName, "B6", len(results))

	// 統計資訊
	successfulCount := 0
	var totalDuration uint32
	for _, result := range results {
		if result.CalculationDuration != nil {
			successfulCount++
			totalDuration += *result.CalculationDuration
		}
	}

	f.SetCellValue(sheetName, "A7", "成功計算組合數:")
	f.SetCellValue(sheetName, "B7", successfulCount)
	f.SetCellValue(sheetName, "A8", "失敗組合數:")
	f.SetCellValue(sheetName, "B8", len(results)-successfulCount)

	if successfulCount > 0 {
		avgDuration := float64(totalDuration) / float64(successfulCount)
		f.SetCellValue(sheetName, "A9", "平均計算時間(秒):")
		f.SetCellValue(sheetName, "B9", fmt.Sprintf("%.2f", avgDuration))
	}

	// 設定樣式
	style, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true, Size: 14},
	})
	f.SetCellStyle(sheetName, "A1", "A1", style)

	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true},
	})
	f.SetCellStyle(sheetName, "A3", "A9", headerStyle)
}

// createPredictNumbersSheet 建立預測號碼工作表
func (ctrl *BallFollowExportController) createPredictNumbersSheet(f *excelize.File, results []BallFollowAnalysisResult) {
	sheetName := "預測號碼統計"
	f.NewSheet(sheetName)

	// 設定標題行
	headers := []string{"參數組合", "拖牌組合", "推算期數", "拖牌區間", "預測號碼", "出現次數", "機率"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header)
	}

	// 填入資料
	row := 2
	for _, result := range results {
		paramComb := fmt.Sprintf("(%d,%d,%d)", result.Comb1, result.Comb2, result.Comb3)
		dragComb := fmt.Sprintf("%d-%d-%d", result.Comb1, result.Comb2, result.Comb3)

		// 處理預測號碼資料
		if predictData, ok := result.PredictNumbers["numbers"].([]interface{}); ok {
			for _, numInterface := range predictData {
				if num, ok := numInterface.(float64); ok {
					f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), paramComb)
					f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), dragComb)
					f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), result.PeriodNum)
					f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), result.MaxRange)
					f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), int(num))

					// 取得出現次數和機率
					numStr := fmt.Sprintf("%.0f", num)
					if appearances, ok := result.PredictNumbers["appearances"].(map[string]interface{}); ok {
						if count, ok := appearances[numStr].(float64); ok {
							f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), int(count))
						}
					}
					if probabilities, ok := result.PredictNumbers["probabilities"].(map[string]interface{}); ok {
						if prob, ok := probabilities[numStr].(float64); ok {
							f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), fmt.Sprintf("%.4f", prob))
						}
					}

					row++
				}
			}
		}
	}

	// 設定標題樣式
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"E0E0E0"}, Pattern: 1},
	})
	f.SetCellStyle(sheetName, "A1", "G1", headerStyle)

	// 自動調整欄寬
	f.SetColWidth(sheetName, "A", "G", 15)
}

// createNonAppearedNumbersSheet 建立未出現號碼工作表
func (ctrl *BallFollowExportController) createNonAppearedNumbersSheet(f *excelize.File, results []BallFollowAnalysisResult) {
	sheetName := "未出現號碼"
	f.NewSheet(sheetName)

	// 設定標題行
	headers := []string{"參數組合", "拖牌組合", "推算期數", "拖牌區間", "未出現號碼"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header)
	}

	// 填入資料
	row := 2
	for _, result := range results {
		paramComb := fmt.Sprintf("(%d,%d,%d)", result.Comb1, result.Comb2, result.Comb3)
		dragComb := fmt.Sprintf("%d-%d-%d", result.Comb1, result.Comb2, result.Comb3)

		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), paramComb)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), dragComb)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), result.PeriodNum)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), result.MaxRange)

		// 處理未出現號碼資料
		if nonAppearedData, ok := result.NonAppearedNumbers["numbers"].([]interface{}); ok {
			var numbers []string
			for _, numInterface := range nonAppearedData {
				if num, ok := numInterface.(float64); ok {
					numbers = append(numbers, fmt.Sprintf("%.0f", num))
				}
			}

			// 每10個號碼一行
			numbersStr := ""
			for i, num := range numbers {
				if i > 0 && i%10 == 0 {
					numbersStr += "\n"
				}
				if i > 0 && i%10 != 0 {
					numbersStr += ", "
				}
				numbersStr += num
			}

			f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), numbersStr)
		}

		row++
	}

	// 設定標題樣式
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"E0E0E0"}, Pattern: 1},
	})
	f.SetCellStyle(sheetName, "A1", "E1", headerStyle)

	// 自動調整欄寬
	f.SetColWidth(sheetName, "A", "D", 15)
	f.SetColWidth(sheetName, "E", "E", 50)
}

// createTailStatisticsSheet 建立尾數統計工作表
func (ctrl *BallFollowExportController) createTailStatisticsSheet(f *excelize.File, results []BallFollowAnalysisResult) {
	sheetName := "尾數統計"
	f.NewSheet(sheetName)

	// 設定標題行
	headers := []string{"參數組合", "拖牌組合", "推算期數", "拖牌區間", "尾數", "出現次數", "機率"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header)
	}

	// 填入資料
	row := 2
	for _, result := range results {
		paramComb := fmt.Sprintf("(%d,%d,%d)", result.Comb1, result.Comb2, result.Comb3)
		dragComb := fmt.Sprintf("%d-%d-%d", result.Comb1, result.Comb2, result.Comb3)

		// 處理尾數統計資料
		if tailAppearances, ok := result.TailStatistics["tail_appearances"].(map[string]interface{}); ok {
			for tailStr, countInterface := range tailAppearances {
				if count, ok := countInterface.(float64); ok {
					f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), paramComb)
					f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), dragComb)
					f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), result.PeriodNum)
					f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), result.MaxRange)
					f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), tailStr)
					f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), int(count))

					// 取得機率
					if tailProbs, ok := result.TailStatistics["tail_probabilities"].(map[string]interface{}); ok {
						if prob, ok := tailProbs[tailStr].(float64); ok {
							f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), fmt.Sprintf("%.4f", prob))
						}
					}

					row++
				}
			}
		}
	}

	// 設定標題樣式
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"E0E0E0"}, Pattern: 1},
	})
	f.SetCellStyle(sheetName, "A1", "G1", headerStyle)

	// 自動調整欄寬
	f.SetColWidth(sheetName, "A", "G", 15)
}

// createParameterCombinationsSheet 建立參數組合工作表
func (ctrl *BallFollowExportController) createParameterCombinationsSheet(f *excelize.File, results []BallFollowAnalysisResult) {
	sheetName := "參數組合列表"
	f.NewSheet(sheetName)

	// 設定標題行
	headers := []string{"序號", "拖牌組合", "推算期數", "拖牌區間", "分析期數", "計算時間(秒)", "計算狀態", "建立時間"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header)
	}

	// 填入資料
	for i, result := range results {
		row := i + 2
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), i+1)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), fmt.Sprintf("(%d,%d,%d)", result.Comb1, result.Comb2, result.Comb3))
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), result.PeriodNum)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), result.MaxRange)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), result.AnalysisPeriods)

		if result.CalculationDuration != nil {
			f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), *result.CalculationDuration)
			f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), "成功")
		} else {
			f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), "-")
			f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), "失敗")
		}

		f.SetCellValue(sheetName, fmt.Sprintf("H%d", row), result.CreatedAt.Format("2006-01-02 15:04:05"))
	}

	// 設定標題樣式
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"E0E0E0"}, Pattern: 1},
	})
	f.SetCellStyle(sheetName, "A1", "H1", headerStyle)

	// 自動調整欄寬
	f.SetColWidth(sheetName, "A", "H", 15)
}
